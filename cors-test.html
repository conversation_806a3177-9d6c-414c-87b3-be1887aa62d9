<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin: 10px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: white;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
    </style>
</head>
<body>
    <h1>Ball Game API CORS 测试</h1>
    
    <div class="container">
        <h3>测试提交数据 (POST)</h3>
        <button onclick="testSubmitData()">测试 POST /ball-game/group-submit</button>
        <div id="submitResult" class="result" style="display: none;"></div>
    </div>
    
    <div class="container">
        <h3>测试获取数据 (GET)</h3>
        <button onclick="testGetData()">测试 GET /ball-game/group-list</button>
        <div id="getResult" class="result" style="display: none;"></div>
    </div>
    
    <div class="container">
        <h3>测试预检请求 (OPTIONS)</h3>
        <button onclick="testOptions()">测试 OPTIONS 预检请求</button>
        <div id="optionsResult" class="result" style="display: none;"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8083';

        function showResult(elementId, message, isError = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${isError ? 'error' : 'success'}`;
            element.innerHTML = message;
        }

        async function testSubmitData() {
            try {
                const testData = {
                    group: Math.floor(Math.random() * 1000),
                    redBallCount: 10,
                    blueBallCount: 5,
                    prediction: "red"
                };

                const response = await fetch(`${API_BASE_URL}/ball-game/group-submit`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });

                if (response.ok) {
                    const result = await response.json();
                    showResult('submitResult', 
                        `✅ POST 请求成功！<br>
                        状态码: ${response.status}<br>
                        返回数据: <pre>${JSON.stringify(result, null, 2)}</pre>`
                    );
                } else {
                    showResult('submitResult', 
                        `❌ POST 请求失败！<br>状态码: ${response.status}<br>错误信息: ${response.statusText}`, 
                        true
                    );
                }
            } catch (error) {
                showResult('submitResult', 
                    `❌ POST 请求出错！<br>错误信息: ${error.message}<br>
                    这通常是CORS错误，请检查后端CORS配置。`, 
                    true
                );
            }
        }

        async function testGetData() {
            try {
                const response = await fetch(`${API_BASE_URL}/ball-game/group-list`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    showResult('getResult', 
                        `✅ GET 请求成功！<br>
                        状态码: ${response.status}<br>
                        返回数据: <pre>${JSON.stringify(result, null, 2)}</pre>`
                    );
                } else {
                    showResult('getResult', 
                        `❌ GET 请求失败！<br>状态码: ${response.status}<br>错误信息: ${response.statusText}`, 
                        true
                    );
                }
            } catch (error) {
                showResult('getResult', 
                    `❌ GET 请求出错！<br>错误信息: ${error.message}<br>
                    这通常是CORS错误，请检查后端CORS配置。`, 
                    true
                );
            }
        }

        async function testOptions() {
            try {
                const response = await fetch(`${API_BASE_URL}/ball-game/group-submit`, {
                    method: 'OPTIONS',
                    headers: {
                        'Access-Control-Request-Method': 'POST',
                        'Access-Control-Request-Headers': 'Content-Type',
                    }
                });

                const corsHeaders = {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
                    'Access-Control-Max-Age': response.headers.get('Access-Control-Max-Age')
                };

                showResult('optionsResult', 
                    `✅ OPTIONS 预检请求成功！<br>
                    状态码: ${response.status}<br>
                    CORS 响应头: <pre>${JSON.stringify(corsHeaders, null, 2)}</pre>`
                );
            } catch (error) {
                showResult('optionsResult', 
                    `❌ OPTIONS 请求出错！<br>错误信息: ${error.message}`, 
                    true
                );
            }
        }

        // 页面加载时显示当前配置
        window.onload = function() {
            console.log('CORS 测试页面已加载');
            console.log('API 基础地址:', API_BASE_URL);
        };
    </script>
</body>
</html>
