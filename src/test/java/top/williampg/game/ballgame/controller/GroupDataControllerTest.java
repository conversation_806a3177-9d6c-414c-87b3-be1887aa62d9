package top.williampg.game.ballgame.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureTestMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import top.williampg.game.ballgame.dto.GroupSubmitRequest;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureTestMvc
class GroupDataControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void testSubmitGroupData() throws Exception {
        GroupSubmitRequest request = new GroupSubmitRequest();
        request.setGroup(1);
        request.setRedBallCount(10);
        request.setBlueBallCount(0);
        request.setPrediction("red");

        mockMvc.perform(post("/ball-game/group-submit")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.group").value(1))
                .andExpect(jsonPath("$.redBallCount").value(10))
                .andExpect(jsonPath("$.blueBallCount").value(0))
                .andExpect(jsonPath("$.prediction").value("red"));
    }

    @Test
    void testGetGroupList() throws Exception {
        mockMvc.perform(get("/ball-game/group-list"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));
    }
}
