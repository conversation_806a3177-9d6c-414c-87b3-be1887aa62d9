package top.williampg.game.ballgame.entity;

import jakarta.persistence.*;
import lombok.Data;

/**
 * 组数据实体类
 */
@Entity
@Table(name = "group_data")
@Data
public class GroupData {
    
    /**
     * 组号（主键）
     */
    @Id
    @Column(name = "group")
    private Integer group;

    /**
     * 红球数量
     */
    @Column(name = "redBallCount")
    private Integer redBallCount;

    /**
     * 蓝球数量
     */
    @Column(name = "blueBallCount")
    private Integer blueBallCount;
    
    /**
     * 预测结果
     */
    @Column(name = "prediction")
    private String prediction;
}
