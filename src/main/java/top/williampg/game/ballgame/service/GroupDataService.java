package top.williampg.game.ballgame.service;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import top.williampg.game.ballgame.dto.GroupSubmitRequest;
import top.williampg.game.ballgame.entity.GroupData;
import top.williampg.game.ballgame.repository.GroupDataRepository;

import java.util.List;

/**
 * 组数据服务类
 */
@Service
@RequiredArgsConstructor
public class GroupDataService {
    
    private final GroupDataRepository groupDataRepository;
    
    /**
     * 保存或更新组数据
     * 如果组号已存在则覆盖，否则新增
     */
    public GroupData saveOrUpdateGroupData(GroupSubmitRequest request) {
        GroupData groupData = new GroupData();
        groupData.setGroup(request.getGroup());
        groupData.setRedBallCount(request.getRedBallCount());
        groupData.setBlueBallCount(request.getBlueBallCount());
        groupData.setPrediction(request.getPrediction());
        
        return groupDataRepository.save(groupData);
    }
    
    /**
     * 获取所有组数据
     */
    public List<GroupData> getAllGroupData() {
        return groupDataRepository.findAll();
    }
}
