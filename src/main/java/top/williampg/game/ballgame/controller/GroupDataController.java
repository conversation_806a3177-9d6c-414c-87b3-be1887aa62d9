package top.williampg.game.ballgame.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import top.williampg.game.ballgame.dto.GroupSubmitRequest;
import top.williampg.game.ballgame.entity.GroupData;
import top.williampg.game.ballgame.service.GroupDataService;

import java.util.List;

/**
 * 组数据控制器
 */
@RestController
@RequestMapping("/ball-game")
@RequiredArgsConstructor
@CrossOrigin(originPatterns = "*", maxAge = 3600, allowCredentials = "true")
public class GroupDataController {
    
    private final GroupDataService groupDataService;
    
    /**
     * 提交组数据
     * 接口地址：POST /ball-game/group-submit
     * 请求格式：{"group":1,"redBallCount":10,"blueBallCount":0,"prediction":"red"}
     */
    @PostMapping("/group-submit")
    public ResponseEntity<GroupData> submitGroupData(@RequestBody GroupSubmitRequest request) {
        GroupData savedData = groupDataService.saveOrUpdateGroupData(request);
        return ResponseEntity.ok(savedData);
    }
    
    /**
     * 获取所有组数据列表
     * 接口地址：GET /ball-game/group-list
     */
    @GetMapping("/group-list")
    public ResponseEntity<List<GroupData>> getGroupList() {
        List<GroupData> groupDataList = groupDataService.getAllGroupData();
        return ResponseEntity.ok(groupDataList);
    }
}
