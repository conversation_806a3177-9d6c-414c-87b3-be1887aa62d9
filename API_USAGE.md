# Ball Game API 使用说明

## 项目配置

- **端口**: 8083
- **数据库**: MySQL localhost:3306/ball
- **用户名/密码**: root/root
- **CORS**: 已配置允许所有源的跨域访问

## 接口说明

### 1. 提交组数据

**接口地址**: `POST /ball-game/group-submit`

**请求格式**: JSO<PERSON>
```json
{
  "group": 1,
  "redBallCount": 10,
  "blueBallCount": 0,
  "prediction": "red"
}
```

**功能**: 
- 将接收到的JSON数据存入数据库的`group_data`表
- 如果`group`字段在表中已存在，则覆盖保存该条数据
- `group`字段为主键

**响应示例**:
```json
{
  "group": 1,
  "redBallCount": 10,
  "blueBallCount": 0,
  "prediction": "red"
}
```

### 2. 查询所有组数据

**接口地址**: `GET /ball-game/group-list`

**功能**: 返回`group_data`表中全部数据组成的列表

**响应示例**:
```json
[
  {
    "group": 1,
    "redBallCount": 10,
    "blueBallCount": 0,
    "prediction": "red"
  },
  {
    "group": 2,
    "redBallCount": 5,
    "blueBallCount": 3,
    "prediction": "blue"
  }
]
```

## 数据库表结构

表名: `group_data`

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| group | INT | 组号 | 主键 |
| redBallCount | INT | 红球数量 | |
| blueBallCount | INT | 蓝球数量 | |
| prediction | VARCHAR | 预测结果 | |

## 启动应用

1. 确保MySQL数据库运行在localhost:3306，并创建名为`ball`的数据库
2. 运行以下命令启动应用：
   ```bash
   ./mvnw spring-boot:run
   ```
3. 应用将在端口8083启动

## 测试接口

可以使用curl命令测试接口：

```bash
# 提交数据
curl -X POST http://localhost:8083/ball-game/group-submit \
  -H "Content-Type: application/json" \
  -d '{"group":1,"redBallCount":10,"blueBallCount":0,"prediction":"red"}'

# 查询数据
curl http://localhost:8083/ball-game/group-list
```

## CORS 跨域支持

项目已配置完整的CORS支持，允许前端应用从不同域名访问API：

### 支持的特性
- ✅ 允许所有源地址访问
- ✅ 支持所有HTTP方法（GET, POST, PUT, DELETE, OPTIONS）
- ✅ 允许所有请求头
- ✅ 支持携带凭证（cookies）
- ✅ 预检请求缓存3600秒

### 前端调用示例

**JavaScript Fetch API:**
```javascript
// POST 请求示例
fetch('http://localhost:8083/ball-game/group-submit', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        group: 1,
        redBallCount: 10,
        blueBallCount: 0,
        prediction: "red"
    })
})
.then(response => response.json())
.then(data => console.log(data));

// GET 请求示例
fetch('http://localhost:8083/ball-game/group-list')
.then(response => response.json())
.then(data => console.log(data));
```

**jQuery Ajax:**
```javascript
// POST 请求
$.ajax({
    url: 'http://localhost:8083/ball-game/group-submit',
    method: 'POST',
    contentType: 'application/json',
    data: JSON.stringify({
        group: 1,
        redBallCount: 10,
        blueBallCount: 0,
        prediction: "red"
    }),
    success: function(data) {
        console.log(data);
    }
});

// GET 请求
$.get('http://localhost:8083/ball-game/group-list', function(data) {
    console.log(data);
});
```

### 测试CORS配置

项目根目录下的 `cors-test.html` 文件可以用来测试CORS配置是否正常工作：

1. 启动后端应用
2. 在浏览器中打开 `cors-test.html` 文件
3. 点击测试按钮验证跨域请求是否成功
