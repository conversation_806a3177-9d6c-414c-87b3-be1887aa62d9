# CORS 跨域问题解决方案

## 问题描述
前端访问后端API时出现跨域错误，浏览器阻止了来自不同源的请求。

## 解决方案

### 1. 全局CORS配置类
创建了 `src/main/java/top/williampg/game/ballgame/config/CorsConfig.java`

**配置特性：**
- ✅ 允许所有源地址 (`allowedOriginPatterns("*")`)
- ✅ 允许所有HTTP方法 (`GET`, `POST`, `PUT`, `DELETE`, `OPTIONS`)
- ✅ 允许所有请求头 (`allowedHeaders("*")`)
- ✅ 允许携带凭证 (`allowCredentials(true)`)
- ✅ 预检请求缓存3600秒 (`maxAge(3600)`)

### 2. Controller级别的CORS注解
在 `GroupDataController` 类上添加了 `@CrossOrigin` 注解：
```java
@CrossOrigin(origins = "*", maxAge = 3600)
```

### 3. 双重保障机制
- **WebMvcConfigurer**: 全局配置，覆盖所有接口
- **CorsConfigurationSource**: Bean配置，作为备用方案
- **@CrossOrigin注解**: Controller级别配置，额外保障

## 配置详解

### CorsConfig.java 关键配置

```java
@Configuration
public class CorsConfig implements WebMvcConfigurer {
    
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")                    // 所有路径
                .allowedOriginPatterns("*")           // 允许所有源
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")                  // 允许所有请求头
                .allowCredentials(true)               // 允许携带凭证
                .maxAge(3600);                        // 预检缓存时间
    }
}
```

### 支持的请求类型

1. **简单请求**: 直接发送，无需预检
   - GET, POST (content-type: text/plain, application/x-www-form-urlencoded)

2. **复杂请求**: 需要预检 (OPTIONS)
   - POST (content-type: application/json)
   - PUT, DELETE
   - 自定义请求头

## 测试验证

### 1. 使用提供的测试页面
打开项目根目录下的 `cors-test.html` 文件：
- 测试GET请求
- 测试POST请求  
- 测试OPTIONS预检请求

### 2. 浏览器开发者工具检查
在Network标签页中查看：
- OPTIONS预检请求是否成功 (状态码200)
- 响应头是否包含正确的CORS头信息
- 实际请求是否成功发送

### 3. 前端代码示例

**Fetch API:**
```javascript
fetch('http://localhost:8083/ball-game/group-submit', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        group: 1,
        redBallCount: 10,
        blueBallCount: 0,
        prediction: "red"
    })
})
.then(response => response.json())
.then(data => console.log('Success:', data))
.catch(error => console.error('Error:', error));
```

**Axios:**
```javascript
axios.post('http://localhost:8083/ball-game/group-submit', {
    group: 1,
    redBallCount: 10,
    blueBallCount: 0,
    prediction: "red"
})
.then(response => console.log(response.data))
.catch(error => console.error(error));
```

## 常见CORS错误及解决

### 1. "Access to fetch at ... has been blocked by CORS policy"
**原因**: 后端未配置CORS或配置不正确
**解决**: 使用本方案的配置

### 2. "Response to preflight request doesn't pass access control check"
**原因**: OPTIONS预检请求失败
**解决**: 确保配置中包含 `allowedMethods("OPTIONS")`

### 3. "The request client is not a secure context"
**原因**: HTTPS/HTTP混合请求
**解决**: 确保前后端使用相同协议

## 安全考虑

### 生产环境建议
```java
// 生产环境应指定具体的域名
.allowedOrigins("https://yourdomain.com", "https://www.yourdomain.com")
// 而不是使用通配符
.allowedOriginPatterns("*")
```

### 配置示例（生产环境）
```java
registry.addMapping("/ball-game/**")
        .allowedOrigins("https://yourdomain.com")
        .allowedMethods("GET", "POST")
        .allowedHeaders("Content-Type", "Authorization")
        .allowCredentials(true)
        .maxAge(3600);
```

## 验证步骤

1. **启动应用**: `./mvnw spring-boot:run`
2. **打开测试页面**: 在浏览器中打开 `cors-test.html`
3. **执行测试**: 点击各个测试按钮
4. **检查结果**: 确认所有请求都成功

## 故障排除

如果仍有CORS问题：

1. **检查浏览器控制台**: 查看具体错误信息
2. **检查Network标签**: 确认OPTIONS请求状态
3. **重启应用**: 确保配置生效
4. **清除浏览器缓存**: 避免缓存影响
5. **检查防火墙**: 确保端口8083可访问

## 总结

通过以上配置，项目现在支持：
- ✅ 所有源的跨域访问
- ✅ 所有HTTP方法
- ✅ 所有请求头
- ✅ 携带凭证的请求
- ✅ 预检请求缓存

前端应用现在可以无障碍地访问后端API接口。
